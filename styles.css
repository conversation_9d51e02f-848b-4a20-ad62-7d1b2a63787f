/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00f5ff;
    --secondary-color: #ff006e;
    --accent-color: #8338ec;
    --bg-dark: #0a0a0a;
    --bg-darker: #050505;
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --gradient-primary: linear-gradient(135deg, #00f5ff, #8338ec);
    --gradient-secondary: linear-gradient(135deg, #ff006e, #8338ec);
    --shadow-glow: 0 0 20px rgba(0, 245, 255, 0.3);
    --border-glow: 1px solid rgba(0, 245, 255, 0.5);
}

body {
    font-family: 'Exo 2', sans-serif;
    background: var(--bg-dark);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: var(--border-glow);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 70px;
}

.nav-logo .logo-text {
    font-family: 'Orbitron', monospace;
    font-size: 1.8rem;
    font-weight: 900;
    color: var(--text-light);
}

.logo-accent {
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--text-gray);
    text-decoration: none;
    font-weight: 400;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: var(--primary-color);
    text-shadow: 0 0 5px var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    margin: 3px 0;
    transition: 0.3s;
    box-shadow: 0 0 5px var(--primary-color);
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(131, 56, 236, 0.1) 0%, var(--bg-darker) 70%);
}

.grid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        linear-gradient(rgba(0, 245, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0, 245, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
}

@keyframes gridMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="1" fill="%2300f5ff" opacity="0.5"/></svg>') repeat;
    background-size: 100px 100px;
    animation: float 15s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.hero-content {
    z-index: 2;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-family: 'Orbitron', monospace;
    margin-bottom: 1.5rem;
}

.title-line {
    display: block;
    font-size: 1.5rem;
    font-weight: 400;
    color: var(--text-gray);
    margin-bottom: 0.5rem;
}

.title-main {
    display: block;
    font-size: 4rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { filter: brightness(1); }
    to { filter: brightness(1.2); }
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-gray);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-bottom: 3rem;
}

.btn {
    padding: 12px 30px;
    border: none;
    border-radius: 30px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--bg-dark);
    box-shadow: var(--shadow-glow);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 0 30px rgba(0, 245, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: var(--bg-dark);
    box-shadow: var(--shadow-glow);
}

.btn-outline {
    background: transparent;
    color: var(--text-light);
    border: 2px solid var(--text-gray);
}

.btn-outline:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 245, 255, 0.3);
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 3rem;
    z-index: 2;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-shadow: 0 0 10px var(--primary-color);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: var(--text-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
}

/* Section Styles */
section {
    padding: 100px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.1rem;
    color: var(--text-gray);
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: linear-gradient(135deg, rgba(131, 56, 236, 0.05) 0%, rgba(255, 0, 110, 0.05) 100%);
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.about-card {
    background: rgba(255, 255, 255, 0.05);
    border: var(--border-glow);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.about-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-glow);
    border-color: var(--primary-color);
}

.card-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    filter: grayscale(0) hue-rotate(180deg);
}

.about-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--primary-color);
}

.about-card p {
    color: var(--text-gray);
    line-height: 1.6;
}

/* Programs Section */
.programs {
    background: var(--bg-darker);
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 2rem;
}

.program-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.program-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 245, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.program-card:hover::before {
    left: 100%;
}

.program-card.featured {
    border: var(--border-glow);
    box-shadow: var(--shadow-glow);
    transform: scale(1.05);
}

.program-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 0 25px rgba(0, 245, 255, 0.3);
}

.program-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.program-header h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.4rem;
    color: var(--primary-color);
}

.program-duration {
    background: var(--gradient-secondary);
    color: var(--text-light);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.program-features {
    list-style: none;
    margin-bottom: 2rem;
}

.program-features li {
    padding: 0.5rem 0;
    color: var(--text-gray);
    position: relative;
    padding-left: 1.5rem;
}

.program-features li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--primary-color);
    font-size: 0.8rem;
}

/* Technology Section */
.technology {
    background: linear-gradient(135deg, rgba(0, 245, 255, 0.05) 0%, rgba(131, 56, 236, 0.05) 100%);
}

.tech-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.tech-features {
    margin-top: 2rem;
}

.tech-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    color: var(--text-gray);
}

.feature-icon {
    font-size: 1.5rem;
    filter: grayscale(0) hue-rotate(180deg);
}

.tech-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.tech-circle {
    width: 300px;
    height: 300px;
    border: 3px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    animation: rotate 10s linear infinite;
    box-shadow: 0 0 50px rgba(0, 245, 255, 0.3);
}

.tech-circle::before {
    content: '';
    position: absolute;
    width: 250px;
    height: 250px;
    border: 2px solid var(--accent-color);
    border-radius: 50%;
    animation: rotate 8s linear infinite reverse;
}

.tech-circle::after {
    content: '';
    position: absolute;
    width: 200px;
    height: 200px;
    border: 1px solid var(--secondary-color);
    border-radius: 50%;
    animation: rotate 6s linear infinite;
}

@keyframes rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.circle-content {
    z-index: 2;
    text-align: center;
}

.circle-text {
    font-family: 'Orbitron', monospace;
    font-size: 3rem;
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Contact Section */
.contact {
    background: var(--bg-darker);
}

.contact-form {
    max-width: 600px;
    margin: 0 auto;
}

.form-group {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1rem;
}

.contact-form input,
.contact-form select,
.contact-form textarea {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 1rem;
    color: var(--text-light);
    font-family: inherit;
    transition: all 0.3s ease;
}

.contact-form input:focus,
.contact-form select:focus,
.contact-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.contact-form textarea {
    grid-column: 1 / -1;
    resize: vertical;
    min-height: 120px;
    margin-bottom: 1rem;
}

/* Footer */
.footer {
    background: var(--bg-darker);
    border-top: var(--border-glow);
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer p {
    color: var(--text-gray);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: rgba(10, 10, 10, 0.95);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        backdrop-filter: blur(10px);
        padding: 2rem 0;
    }

    .nav-menu.active {
        left: 0;
    }

    .title-main {
        font-size: 2.5rem;
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .tech-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .tech-circle {
        width: 200px;
        height: 200px;
    }

    .tech-circle::before {
        width: 160px;
        height: 160px;
    }

    .tech-circle::after {
        width: 120px;
        height: 120px;
    }

    .form-group {
        grid-template-columns: 1fr;
    }
}
